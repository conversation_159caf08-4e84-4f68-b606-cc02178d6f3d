#!/usr/bin/env python3
"""
Script to filter MTN services data based on specific criteria:
- Find unique MSISDN & service combinations
- Track first ACT-SB status with price 0
- Check if MSISDN later appears with RSC-BL and price > 0
- Create subscribed column (1 if RSC-BL with price > 0, 0 otherwise)
"""

import csv
import sys
from datetime import datetime
from collections import defaultdict

def process_mtn_services(input_file, output_file):
    """
    Process MTN services CSV file according to specified criteria

    Args:
        input_file (str): Path to input CSV file
        output_file (str): Path to output CSV file
    """

    print(f"Reading data from {input_file}...")

    # Read the CSV file with proper quoting to handle commas in fields
    try:
        with open(input_file, 'r') as f:
            reader = csv.DictReader(f)
            records = list(reader)
        print(f"Loaded {len(records)} records")
    except Exception as e:
        print(f"Error reading file: {e}")
        return

    print("Processing records...")

    # Group records by MSISDN and service
    groups = defaultdict(list)
    for record in records:
        key = (record['MSISDN'], record['service'])
        groups[key].append(record)

    # Sort each group by datetime
    for key in groups:
        groups[key].sort(key=lambda x: datetime.strptime(x['datetime'], '%Y-%m-%d %H:%M:%S'))

    results = []

    for (msisdn, service), group in groups.items():
        # Find first ACT-SB record with price 0
        first_act_sb = None
        for record in group:
            try:
                price = int(record['Price']) if record['Price'].strip() else 0
            except (ValueError, AttributeError):
                price = 0

            if record['STATUS'] == 'ACT-SB' and price == 0:
                first_act_sb = record.copy()
                break

        if first_act_sb is None:
            # Skip if no ACT-SB with price 0 found
            continue

        # Check if this MSISDN+service combination later has RSC-BL with price > 0
        has_rsc_bl = False
        for record in group:
            try:
                price = int(record['Price']) if record['Price'].strip() else 0
            except (ValueError, AttributeError):
                price = 0

            if record['STATUS'] == 'RSC-BL' and price > 0:
                has_rsc_bl = True
                break

        # Set subscribed flag
        first_act_sb['subscribed'] = '1' if has_rsc_bl else '0'

        results.append(first_act_sb)

    # Sort results by datetime
    results.sort(key=lambda x: datetime.strptime(x['datetime'], '%Y-%m-%d %H:%M:%S'))

    if results:
        print(f"Processed {len(results)} unique MSISDN-service combinations")

        subscribed_count = sum(1 for r in results if r['subscribed'] == '1')
        not_subscribed_count = len(results) - subscribed_count

        print(f"Subscribed (1): {subscribed_count}")
        print(f"Not subscribed (0): {not_subscribed_count}")

        # Save to output file
        fieldnames = ['datetime', 'click_id', 'STATUS', 'MSISDN', 'Price', 'service', 'subscribed']

        with open(output_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, quoting=csv.QUOTE_MINIMAL)
            writer.writeheader()
            writer.writerows(results)

        print(f"Results saved to {output_file}")

        # Display first few records as preview
        print("\nPreview of results:")
        print("datetime,click_id,STATUS,MSISDN,Price,service,subscribed")
        for i, record in enumerate(results[:10]):
            print(f"{record['datetime']},{record['click_id']},{record['STATUS']},{record['MSISDN']},{record['Price']},{record['service']},{record['subscribed']}")

    else:
        print("No records found matching the criteria")

def main():
    input_file = "mtn_services.csv"
    output_file = "mtn_services_filtered.csv"
    
    print("MTN Services Data Filter")
    print("=" * 50)
    print("Criteria:")
    print("- Find first ACT-SB status with price 0 for each MSISDN+service")
    print("- Check if MSISDN later appears with RSC-BL and price > 0")
    print("- Add 'subscribed' column (1 if RSC-BL found, 0 otherwise)")
    print("=" * 50)
    
    process_mtn_services(input_file, output_file)

if __name__ == "__main__":
    main()
