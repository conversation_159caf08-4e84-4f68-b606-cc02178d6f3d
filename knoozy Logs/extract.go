package main

import (
	"bufio"
	"encoding/csv"
	"flag"
	"fmt"
	"os"
	"regexp"
	"strings"
	"time"
)

type Record struct {
	DateTime string
	ClickID  string
	Status   string
	MSISDN   string
	Price    string
}

func main() {
	logFile := "laravel.log"
	outputFile := "output.csv"

	fromStr := flag.String("from", "", "From date (inclusive) in format YYYY-MM-DD")
	toStr := flag.String("to", "", "To date (inclusive) in format YYYY-MM-DD")
	flag.Parse()

	var fromDate, toDate time.Time
	var err error

	if *fromStr != "" {
		fromDate, err = time.Parse("2006-01-02", *fromStr)
		if err != nil {
			fmt.Printf("Invalid from date: %v\n", err)
			return
		}
	}
	if *toStr != "" {
		toDate, err = time.Parse("2006-01-02", *toStr)
		if err != nil {
			fmt.Printf("Invalid to date: %v\n", err)
			return
		}
		// Include the full day for "to"
		toDate = toDate.Add(24 * time.Hour)
	}

	file, err := os.Open(logFile)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	out, err := os.Create(outputFile)
	if err != nil {
		panic(err)
	}
	defer out.Close()

	writer := csv.NewWriter(out)
	defer writer.Flush()
	writer.Write([]string{"datetime", "click_id", "STATUS", "MSISDN", "Price"})

	scanner := bufio.NewScanner(file)
	dateRe := regexp.MustCompile(`^\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] production.DEBUG:`)

	var current Record
	var inRecord bool
	var currentDate time.Time

	for scanner.Scan() {
		line := scanner.Text()

		if dateRe.MatchString(line) {
			matches := dateRe.FindStringSubmatch(line)
			parsedTime, _ := time.Parse("2006-01-02 15:04:05", matches[1])

			// Write previous record before starting new one
			if inRecord &&
				(fromDate.IsZero() || !currentDate.Before(fromDate)) &&
				(toDate.IsZero() || currentDate.Before(toDate)) {
				if current.ClickID == "" {
					current.ClickID = "NULL"
				}
				writer.Write([]string{current.DateTime, current.ClickID, current.Status, current.MSISDN, current.Price})
			}

			current = Record{DateTime: matches[1]}
			currentDate = parsedTime
			inRecord = false
			continue
		}

		if strings.Contains(line, "'STATUS' =>") {
			current.Status = extractValue(line, "'STATUS' =>")
			inRecord = true
		}
		if strings.Contains(line, "'MSISDN' =>") {
			current.MSISDN = extractValue(line, "'MSISDN' =>")
		}
		if strings.Contains(line, "'Price' =>") {
			current.Price = extractValue(line, "'Price' =>")
		}
		if strings.Contains(line, "'click_id' =>") {
			current.ClickID = extractValue(line, "'click_id' =>")
		}
		if strings.Contains(line, "'Tracker' =>") {
			tracker := extractValue(line, "'Tracker' =>")
			if strings.HasPrefix(tracker, "click_id=") {
				current.ClickID = strings.TrimPrefix(tracker, "click_id=")
			} else if tracker == "NULL" && current.ClickID == "" {
				current.ClickID = "NULL"
			}
		}
	}

	// Write last record if in range
	if inRecord &&
		(fromDate.IsZero() || !currentDate.Before(fromDate)) &&
		(toDate.IsZero() || currentDate.Before(toDate)) {
		writer.Write([]string{current.DateTime, current.ClickID, current.Status, current.MSISDN, current.Price})
	}

	fmt.Println("Extraction complete.")
}

func extractValue(line, key string) string {
	start := strings.Index(line, key)
	if start == -1 {
		return ""
	}
	valPart := line[start+len(key):]
	valPart = strings.TrimSpace(valPart)
	valPart = strings.Trim(valPart, "',")
	return valPart
}
