#!/usr/bin/env python3
"""
Script to check for duplicate MSISDNs in qawafi_users.csv
"""

import csv
import sys
from collections import Counter, defaultdict

def check_duplicates():
    print("=" * 60)
    print("CHECKING FOR DUPLICATE MSISDNs IN QAWAFI_USERS.CSV")
    print("=" * 60)
    
    msisdn_counter = Counter()
    msisdn_details = defaultdict(list)
    total_rows = 0
    
    try:
        with open('qawafi_users.csv', 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            print("Reading qawafi_users.csv...")
            
            for row_num, row in enumerate(reader, start=2):  # start=2 because header is line 1
                total_rows += 1
                msisdn = row['MSISDN']
                
                # Count occurrences
                msisdn_counter[msisdn] += 1
                
                # Store details for duplicates analysis
                msisdn_details[msisdn].append({
                    'row_num': row_num,
                    'datetime': row['datetime'],
                    'click_id': row['click_id'],
                    'status': row['STATUS'],
                    'price': row['Price']
                })
                
                if total_rows % 1000 == 0:
                    print(f"Processed {total_rows} rows...")
        
        print(f"Finished processing {total_rows} total rows")
        
    except FileNotFoundError:
        print("Error: qawafi_users.csv file not found in the current directory")
        return False
    except Exception as e:
        print(f"Error reading qawafi_users.csv: {e}")
        return False
    
    # Analyze duplicates
    duplicates = {msisdn: count for msisdn, count in msisdn_counter.items() if count > 1}
    unique_msisdns = len(msisdn_counter)
    
    print("\n" + "=" * 60)
    print("ANALYSIS RESULTS")
    print("=" * 60)
    print(f"Total rows: {total_rows}")
    print(f"Unique MSISDNs: {unique_msisdns}")
    print(f"Duplicate MSISDNs: {len(duplicates)}")
    
    if duplicates:
        print(f"\n❌ DUPLICATES FOUND!")
        print(f"Number of MSISDNs with duplicates: {len(duplicates)}")
        
        total_duplicate_rows = sum(duplicates.values())
        extra_rows = total_duplicate_rows - len(duplicates)
        print(f"Total rows with duplicate MSISDNs: {total_duplicate_rows}")
        print(f"Extra rows due to duplication: {extra_rows}")
        
        print("\nDuplicate MSISDNs and their counts:")
        print("-" * 40)
        
        # Sort duplicates by count (highest first)
        sorted_duplicates = sorted(duplicates.items(), key=lambda x: x[1], reverse=True)
        
        for i, (msisdn, count) in enumerate(sorted_duplicates[:20]):  # Show top 20
            print(f"{i+1:2d}. MSISDN: {msisdn} - appears {count} times")
        
        if len(duplicates) > 20:
            print(f"... and {len(duplicates) - 20} more duplicated MSISDNs")
        
        # Show detailed information for the most duplicated MSISDNs
        print(f"\nDetailed view of top 5 most duplicated MSISDNs:")
        print("-" * 60)
        
        for i, (msisdn, count) in enumerate(sorted_duplicates[:5]):
            print(f"\n{i+1}. MSISDN: {msisdn} (appears {count} times)")
            details = msisdn_details[msisdn]
            for j, detail in enumerate(details):
                click_id_display = detail['click_id'] if detail['click_id'] != 'NULL' else 'NULL'
                print(f"   {j+1}. Row {detail['row_num']}: {detail['datetime']} | {detail['status']} | Click ID: {click_id_display} | Price: {detail['price']}")
        
        # Create a file with duplicate MSISDNs
        print(f"\nCreating duplicate_msisdns_report.csv...")
        try:
            with open('duplicate_msisdns_report.csv', 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['MSISDN', 'duplicate_count', 'first_occurrence', 'last_occurrence']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for msisdn, count in sorted_duplicates:
                    details = msisdn_details[msisdn]
                    first_occurrence = details[0]['datetime']
                    last_occurrence = details[-1]['datetime']
                    
                    writer.writerow({
                        'MSISDN': msisdn,
                        'duplicate_count': count,
                        'first_occurrence': first_occurrence,
                        'last_occurrence': last_occurrence
                    })
            
            print("✅ Created duplicate_msisdns_report.csv")
            
        except Exception as e:
            print(f"Error creating duplicate report: {e}")
    
    else:
        print(f"\n✅ NO DUPLICATES FOUND!")
        print("All MSISDNs in qawafi_users.csv are unique.")
    
    return True

if __name__ == "__main__":
    success = check_duplicates()
    
    if not success:
        print("\nAnalysis failed. Please check the error messages above.")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("DUPLICATE CHECK COMPLETED")
    print("=" * 60)
