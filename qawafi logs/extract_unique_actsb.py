#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to extract all unique MSISDNs with ACT-SB status from qawafi.csv
and save them to a new CSV file.
"""

import csv
import sys
from collections import OrderedDict

def extract_unique_actsb():
    # Dictionary to store unique MSISDNs with their first ACT-SB occurrence
    # Using OrderedDict to maintain the order of first appearance
    unique_msisdns = OrderedDict()
    
    print("Reading qawafi.csv and extracting unique ACT-SB MSISDNs...")
    
    try:
        with open('qawafi.csv', 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            total_rows = 0
            actsb_rows = 0
            
            for row_num, row in enumerate(reader, start=2):  # start=2 because header is line 1
                total_rows += 1
                
                if total_rows % 5000 == 0:
                    print(f"Processed {total_rows} rows...")
                
                status = row['STATUS']
                msisdn = row['MSISDN']
                
                if status == 'ACT-SB':
                    actsb_rows += 1
                    
                    # Only store if we haven't seen this MSISDN before
                    if msisdn not in unique_msisdns:
                        unique_msisdns[msisdn] = row
            
            print(f"Finished processing {total_rows} total rows")
            print(f"Found {actsb_rows} total ACT-SB rows")
            print(f"Found {len(unique_msisdns)} unique MSISDNs with ACT-SB status")
    
    except FileNotFoundError:
        print("Error: qawafi.csv file not found in the current directory")
        return False
    except Exception as e:
        print(f"Error reading qawafi.csv: {e}")
        return False
    
    # Write the unique ACT-SB rows to a new CSV file
    output_filename = 'unique_actsb_msisdns.csv'
    print(f"Writing unique ACT-SB MSISDNs to {output_filename}...")
    
    try:
        with open(output_filename, 'w', newline='', encoding='utf-8') as csvfile:
            if unique_msisdns:
                # Get fieldnames from the first row
                fieldnames = list(unique_msisdns.values())[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                writer.writerows(unique_msisdns.values())
        
        print(f"Successfully created {output_filename} with {len(unique_msisdns)} unique records")
        
        # Print some sample data
        print("\nSample of unique ACT-SB MSISDNs:")
        print("-" * 50)
        sample_count = min(10, len(unique_msisdns))
        for i, (msisdn, row) in enumerate(list(unique_msisdns.items())[:sample_count]):
            click_id = row['click_id'] if row['click_id'] != 'NULL' else 'NULL'
            print(f"{i+1:2d}. MSISDN: {msisdn}, Click ID: {click_id}, Date: {row['datetime']}")
        
        if len(unique_msisdns) > 10:
            print(f"... and {len(unique_msisdns) - 10} more unique MSISDNs")
        
        return True
        
    except Exception as e:
        print(f"Error writing {output_filename}: {e}")
        return False

def create_msisdn_only_file():
    """Create a simple text file with just the MSISDNs (one per line)"""
    try:
        with open('unique_actsb_msisdns.csv', 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            
            with open('unique_msisdns_only.txt', 'w', encoding='utf-8') as txtfile:
                for row in reader:
                    txtfile.write(row['MSISDN'] + '\n')
        
        print("Also created 'unique_msisdns_only.txt' with just the MSISDN numbers")
        return True
        
    except Exception as e:
        print(f"Error creating MSISDN-only file: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("EXTRACTING UNIQUE ACT-SB MSISDNs FROM QAWAFI.CSV")
    print("=" * 60)
    
    success = extract_unique_actsb()
    
    if success:
        print("\n" + "=" * 60)
        print("CREATING ADDITIONAL MSISDN-ONLY FILE")
        print("=" * 60)
        create_msisdn_only_file()
        
        print("\n" + "=" * 60)
        print("PROCESSING COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("Files created:")
        print("1. unique_actsb_msisdns.csv - Full data with all columns")
        print("2. unique_msisdns_only.txt - Just the MSISDN numbers (one per line)")
    else:
        print("\nProcessing failed. Please check the error messages above.")
        sys.exit(1)
