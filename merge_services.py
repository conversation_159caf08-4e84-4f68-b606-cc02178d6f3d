import csv
import os

files_to_merge = [
    {'file': 'coursat logs/coursat.csv', 'service': 'coursat'},
    {'file': 'qawafi logs/qawafi.csv', 'service': 'qawafi'},
    {'file': 'booktown logs/booktown.csv', 'service': 'booktown'},
    {'file': 'gamesclub logs/gamesclub.csv', 'service': 'gamesclub'}
]

output_file = 'mtn_services.csv'
service_counts = {}

with open(output_file, 'w', newline='') as outfile:
    writer = csv.writer(outfile)
    header_written = False
    
    for file_info in files_to_merge:
        file_path = file_info['file']
        service_name = file_info['service']
        
        if os.path.exists(file_path):
            print("Processing {}...".format(file_path))
            
            with open(file_path, 'r') as infile:
                reader = csv.reader(infile)
                header = next(reader)
                
                if not header_written:
                    header.append('service')
                    writer.writerow(header)
                    header_written = True
                
                row_count = 0
                for row in reader:
                    row.append(service_name)
                    writer.writerow(row)
                    row_count += 1
                
                service_counts[service_name] = row_count
                print("Added {} rows from {}".format(row_count, service_name))
        else:
            print("Warning: {} not found!".format(file_path))

if service_counts:
    print("\nMerging complete!")
    print("Output file: {}".format(output_file))
    print("Total rows: {}".format(sum(service_counts.values())))
    print("\nService breakdown:")
    for service, count in service_counts.items():
        print("{}: {} rows".format(service, count))
else:
    print("No files were found to merge!") 